import { io } from 'socket.io-client'
import Cookies from 'js-cookie'
import { useUserStore } from '@/stores/model/user.js'
import { useChatStore } from '@/stores/model/chat.js'

class SocketService {
    constructor() {
        this.socket = null
        this.isConnected = false
        this.reconnectAttempts = 0
        this.maxReconnectAttempts = 5
    }

    connect() {
        const token = Cookies.get('jwt')

        // 临时：即使没有token也允许连接（用于测试）
        const socketOptions = {
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        }

        if (token) {
            socketOptions.query = { token: token }
            console.log('Connecting with JWT token')
        } else {
            console.warn('Connecting without JWT token (test mode)')
        }

        this.socket = io('http://localhost:9092', socketOptions)
        this.setupEventListeners()
    }

    setupEventListeners() {
        if (!this.socket) return

        this.socket.on('connect', () => {
            console.log('Connected to chat server')
            this.isConnected = true
            this.reconnectAttempts = 0
        })

        this.socket.on('disconnect', (reason) => {
            console.log('Disconnected from chat server:', reason)
            this.isConnected = false

            if (reason === 'io server disconnect') {
                // Server disconnected, try to reconnect
                this.reconnect()
            }
        })

        this.socket.on('connect_error', (error) => {
            console.error('Connection error:', error)
            this.isConnected = false
            this.reconnect()
        })

        // 接收消息
        this.socket.on('receiveMessage', (data) => {
            const chatStore = useChatStore()
            const message = JSON.parse(data)
            console.log('收到消息 (receiveMessage):', message)
            chatStore.addMessage(message)
        })

        // 消息发送确认
        this.socket.on('messageConfirm', (data) => {
            const chatStore = useChatStore()
            const userStore = useUserStore()
            const message = JSON.parse(data)
            console.log('消息发送确认 (messageConfirm):', message)

            // 临时修复：如果senderId和recipientId都相同，修正数据
            if (message.senderId === message.recipientId) {
                console.log('🔧 修复错误的消息数据')
                const currentUserId = userStore.user?.id
                const currentChatId = chatStore.currentChat?.id

                if (currentUserId && currentChatId) {
                    message.senderId = currentUserId
                    message.recipientId = currentChatId
                    console.log('🔧 修正后的消息:', message)
                }
            }

            chatStore.confirmMessage(message)
        })

        // 消息发送错误
        this.socket.on('messageError', (error) => {
            console.error('Message error:', error)
            // 可以在这里显示错误提示
        })
    }

    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++
            console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

            setTimeout(() => {
                this.connect()
            }, 1000 * this.reconnectAttempts)
        } else {
            console.error('Max reconnection attempts reached')
        }
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect()
            this.socket = null
            this.isConnected = false
        }
    }

    // 发送消息
    sendMessage(recipientId, content, images = null) {
        if (!this.socket || !this.isConnected) {
            console.error('Socket not connected')
            return false
        }

        const messageData = {
            recipientId,
            content,
            images
        }

        this.socket.emit('sendMessage', JSON.stringify(messageData))
        return true
    }

    // 加入聊天房间
    joinChat(otherUserId) {
        if (!this.socket || !this.isConnected) {
            console.error('Socket not connected')
            return
        }

        this.socket.emit('joinChat', JSON.stringify({ otherUserId }))
    }

    // 离开聊天房间
    leaveChat(otherUserId) {
        if (!this.socket || !this.isConnected) {
            console.error('Socket not connected')
            return
        }

        this.socket.emit('leaveChat', JSON.stringify({ otherUserId }))
    }

    // 检查连接状态
    isSocketConnected() {
        return this.isConnected && this.socket && this.socket.connected
    }
}

// 创建单例实例
export const socketService = new SocketService()

// 自动连接（当用户登录时）
export const initializeSocket = () => {
    const userStore = useUserStore()
    if (userStore.isLogin && !socketService.isSocketConnected()) {
        socketService.connect()
    }
}

// 断开连接（当用户登出时）
export const disconnectSocket = () => {
    socketService.disconnect()
}
