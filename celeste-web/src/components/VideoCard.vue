<script setup>
import playing from '@/assets/lottie/playing.json'
import { useUserStore } from '@/stores/model/user.js'
import { eventBus } from '@/utils/eventBus.js'
import dayjs from 'dayjs'
import { inject } from 'vue'
import { useRoute } from 'vue-router'
import { Vue3Lottie } from 'vue3-lottie'

const props = defineProps(['video', 'img'])

const activeVideo = inject('activeVideo', null)

const route = useRoute()
const userStore = useUserStore()

const play = () => eventBus.emit('play', props.video.id)
</script>

<template>
    <div v-if="img" @click="play" class="relative size-full cursor-pointer overflow-hidden rounded-2xl">
        <img
            alt="poster"
            :src="video.poster"
            :class="{ blur: video.id === activeVideo.id }"
            class="size-full object-cover duration-500 hover:scale-125" />
        <div class="absolute bottom-1 left-2 flex items-center gap-1 text-white">
            <i class="icon-[prime--heart] text-xl" />
            <span>{{ video.likesSum }}</span>
        </div>
        <Vue3Lottie
            v-if="video.id === activeVideo.id"
            width="3rem"
            height="3rem"
            :animationData="playing"
            class="absolute bottom-1/2 right-1/2 translate-x-1/2 translate-y-1/2" />
    </div>

    <div v-else @click="play" class="flex size-full cursor-pointer flex-col overflow-hidden rounded-2xl">
        <div class="basis-2/3 overflow-hidden">
            <img alt="poster" :src="video.poster" class="size-full object-cover duration-500 hover:scale-125" />
        </div>
        <div class="flex basis-1/3 flex-col justify-between bg-white/50 p-3 text-sm">
            <p class="line-clamp-2">{{ video.description }}</p>
            <div v-if="String(userStore.user.id) === route.params.id" class="flex items-center justify-between text-gray-500">
                <div class="flex items-center gap-1">
                    <i class="icon-[prime--heart] text-xl" />
                    <p>{{ video.likesSum }}</p>
                </div>
                <p>{{ dayjs(video.createTime).format('YYYY-MM-DD') }}</p>
            </div>
            <div v-else class="flex justify-between">
                <span>@Cele4TeR</span>
                <span class="text-xs">{{ video.betweenTime }}</span>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
