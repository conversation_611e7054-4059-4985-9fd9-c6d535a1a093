import { request } from '@/utils/service.js'

export const loginApi = ({ usernameOrEmail, password }) => {
    return request({
        url: '/api/user/login',
        method: 'post',
        data: { usernameOrEmail, password },
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

export const askCodeApi = ({ email, type }) => {
    return request({
        url: '/api/user/askCode',
        params: { email, type }
    })
}

export const registerApi = ({ username, password, email, code }) => {
    return request({
        url: '/api/user/register',
        method: 'post',
        data: { username, password, email, code }
    })
}

export const resetConfirmApi = ({ email, code }) => {
    return request({
        url: '/api/user/resetPasswordConfirm',
        method: 'post',
        data: { email, code }
    })
}

export const resetPasswordApi = ({ email, code, password }) => {
    return request({
        url: '/api/user/resetPassword',
        method: 'post',
        data: { email, code, password }
    })
}

export const logoutApi = () => {
    return request({
        url: '/api/user/logout'
    })
}

export const getUserInfoApi = (userId) => {
    return request({
        url: `/api/user/getUserInfo/${userId}`
    })
}

export const uploadAvatarApi = (formData) => {
    return request({
        url: '/api/user/uploadAvatar',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

export const getAdoresApi = (userId) => {
    return request({
        url: `/api/user/getAdores/${userId}`
    })
}

export const getFollowersApi = (userId) => {
    return request({
        url: `/api/user/getFollowers/${userId}`
    })
}

export const adoreUserApi = (userId) => {
    return request({
        url: `/api/user/adoreUser/${userId}`
    })
}

export const unAdoreUserApi = (userId) => {
    return request({
        url: `/api/user/unAdoreUser/${userId}`
    })
}

export const unFollowerUserApi = (userId) => {
    return request({
        url: `/api/user/unFollowerUser/${userId}`
    })
}

export const updateUserApi = ({ description, username, gender, home, typeIds }) => {
    return request({
        url: '/api/user/updateUser',
        method: 'post',
        data: { description, username, gender, home, typeIds }
    })
}

export const getMyCollectionsApi = () => {
    return request({
        url: '/api/collection/getMyCollections'
    })
}

export const createCollectionApi = (name) => {
    return request({
        url: '/api/collection/createCollection',
        params: { name }
    })
}

/**
 * 搜索用户（用于开始新对话）
 */
export const searchUsersApi = (keyword, page = 0, size = 20) => {
    return request({
        url: '/api/user/search',
        method: 'GET',
        params: { keyword, page, size }
    })
}

/**
 * 获取所有用户（用于开始新对话）
 */
export const getAllUsersApi = (page = 0, size = 20) => {
    return request({
        url: '/api/user/all',
        method: 'GET',
        params: { page, size }
    })
}
