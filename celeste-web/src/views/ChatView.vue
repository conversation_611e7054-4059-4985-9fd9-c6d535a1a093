<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useChatStore } from '@/stores/model/chat.js'
import { useUserStore } from '@/stores/model/user.js'
import { socketService, initializeSocket } from '@/services/socketService.js'
import Chat from '@/components/Chat.vue'

import { NInput, NButton, NList, NListItem, NAvatar, NBadge, NEmpty, NSpin } from 'naive-ui'

const router = useRouter()
const chatStore = useChatStore()
const userStore = useUserStore()

const messageInput = ref('')
const messagesContainer = ref(null)
const selectedImages = ref([])

// 计算属性
const currentMessages = computed(() => chatStore.currentMessages)
const chatList = computed(() => chatStore.chatList)
const isLoading = computed(() => chatStore.loading)

// 初始化
onMounted(async () => {
    if (!userStore.isLogin || !userStore.user) {
        router.push('/')
        return
    }

    // 初始化Socket连接
    initializeSocket()

    // 获取聊天列表
    await chatStore.fetchChatList()
})

onUnmounted(() => {
    // 离开当前聊天房间
    if (chatStore.currentChat) {
        socketService.leaveChat(chatStore.currentChat.id)
    }
})

// 选择聊天对象
const selectChat = async (user) => {
    // 离开之前的聊天房间
    if (chatStore.currentChat) {
        socketService.leaveChat(chatStore.currentChat.id)
    }

    // 设置新的聊天对象
    chatStore.setCurrentChat(user)

    // 加入新的聊天房间
    socketService.joinChat(user.id)

    // 获取聊天历史
    await chatStore.fetchChatHistory(user.id)

    // 滚动到底部
    scrollToBottom()
}

// 发送消息
const sendMessage = () => {
    if (!messageInput.value.trim() && selectedImages.value.length === 0) {
        return
    }

    if (!chatStore.currentChat) {
        return
    }

    const content = messageInput.value.trim()
    const images = selectedImages.value.length > 0 ? JSON.stringify(selectedImages.value) : null

    // 添加待发送消息到界面
    chatStore.addPendingMessage(chatStore.currentChat.id, content, images)

    // 通过Socket发送消息
    const success = socketService.sendMessage(chatStore.currentChat.id, content, images)

    if (success) {
        // 清空输入
        messageInput.value = ''
        selectedImages.value = []

        // 滚动到底部
        nextTick(() => {
            scrollToBottom()
        })
    }
}

// 滚动到底部
const scrollToBottom = () => {
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
}

// 处理回车发送
const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
    }
}

// 获取用户头像
const getUserAvatar = (username) => {
    return `http://localhost:8080/api/user/getAvatar/${username}`
}

// 格式化时间
const formatTime = (timeString) => {
    const date = new Date(timeString)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) { // 1分钟内
        return '刚刚'
    } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
    } else {
        return date.toLocaleDateString()
    }
}

// 获取对话中的另一个用户
const getOtherUser = (chat) => {
    if (!chat || !userStore.user) return null
    return chat.sender.id === userStore.user.id ? chat.recipient : chat.sender
}

// 获取对话伙伴的ID
const getOtherUserId = (chat) => {
    if (!chat || !userStore.user) return null
    return chat.sender.id === userStore.user.id ? chat.recipient.id : chat.sender.id
}
</script>

<template>
    <div class="flex h-full bg-white/50 rounded-2xl overflow-hidden">
        <!-- 左侧聊天列表 -->
        <div class="w-80 bg-white/90 border-r border-gray-200 flex flex-col min-h-0">
            <div class="p-4 border-b border-gray-200 flex-shrink-0">
                <h2 class="text-xl font-bold text-gray-800">聊天</h2>
            </div>

            <div class="flex-1 overflow-y-auto min-h-0 messages-scroll">
                <NSpin :show="isLoading && chatList.length === 0">
                    <NList v-if="chatList.length > 0" hoverable clickable>
                        <NListItem
                            v-for="chat in chatList"
                            :key="`chat-${chat.id}`"
                            @click="selectChat(getOtherUser(chat))"
                            class="cursor-pointer hover:bg-gray-50 transition-colors"
                            :class="{ 'bg-blue-50': chatStore.currentChat?.id === getOtherUser(chat)?.id }"
                        >
                            <div class="flex items-center space-x-3 p-2">
                                <NBadge
                                    :value="chatStore.unreadCounts[getOtherUserId(chat)] || 0"
                                    :show="(chatStore.unreadCounts[getOtherUserId(chat)] || 0) > 0"
                                >
                                    <NAvatar
                                        :src="getUserAvatar(getOtherUser(chat)?.username)"
                                        size="large"
                                        fallback-src="/default-avatar.svg"
                                    />
                                </NBadge>
                                <div class="flex-1 min-w-0">
                                    <div class="font-medium text-gray-900 truncate">
                                        {{ getOtherUser(chat)?.username || '未知用户' }}
                                    </div>
                                    <div class="text-sm text-gray-500 truncate">
                                        {{ chat.content || (chat.images ? '图片消息' : '暂无消息') }}
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 flex-shrink-0">
                                    {{ formatTime(chat.createTime) }}
                                </div>
                            </div>
                        </NListItem>
                    </NList>
                    <NEmpty v-else description="暂无聊天记录" class="mt-20" />
                </NSpin>
            </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="flex-1 flex flex-col bg-white/80 min-h-0">
            <!-- 聊天头部 -->
            <div v-if="chatStore.currentChat" class="p-4 border-b border-gray-200 bg-white/90 flex-shrink-0">
                <div class="flex items-center space-x-3">
                    <NAvatar :src="getUserAvatar(chatStore.currentChat.username)" size="medium" />
                    <div>
                        <div class="font-medium text-gray-900">{{ chatStore.currentChat.username }}</div>
                        <div class="text-sm text-gray-500">在线</div>
                    </div>
                </div>
            </div>

            <!-- 消息区域 -->
            <div
                ref="messagesContainer"
                class="flex-1 overflow-y-auto p-4 space-y-4 min-h-0 messages-scroll"
                :class="{ 'flex items-center justify-center': !chatStore.currentChat }"
            >
                <div v-if="!chatStore.currentChat" class="text-center text-gray-500">
                    <div class="text-6xl mb-4">💬</div>
                    <div class="text-xl">选择一个聊天开始对话</div>
                </div>

                <div v-else-if="currentMessages.length === 0" class="text-center text-gray-500">
                    <div class="text-4xl mb-4">🎉</div>
                    <div class="text-lg">开始你们的第一次对话吧！</div>
                </div>

                <div v-else>
                    <Chat
                        v-for="message in currentMessages"
                        :key="message.id"
                        :right="message.senderId === userStore.user?.id"
                        :message="{
                            avatar: getUserAvatar(message.senderId === userStore.user?.id ? userStore.user?.username : chatStore.currentChat?.username),
                            content: message.content,
                            images: message.images,
                            createTime: message.createTime
                        }"
                        :class="{ 'opacity-50': message.pending }"
                    />



                </div>
            </div>

            <!-- 输入区域 -->
            <div v-if="chatStore.currentChat" class="p-4 border-t border-gray-200 bg-white/90 flex-shrink-0">
                <div class="flex space-x-2">
                    <NInput
                        v-model:value="messageInput"
                        type="textarea"
                        placeholder="输入消息..."
                        :autosize="{ minRows: 1, maxRows: 4 }"
                        @keypress="handleKeyPress"
                        class="flex-1"
                    />
                    <NButton
                        type="primary"
                        @click="sendMessage"
                        :disabled="!messageInput.trim() && selectedImages.length === 0"
                    >
                        发送
                    </NButton>
                </div>
            </div>
        </div>

    </div>
</template>

<style scoped>
/* 确保聊天界面正确占用空间 */
.chat-container {
    height: 100%;
    min-height: 0;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 确保 flex 容器正确处理高度 */
.flex-1 {
    flex: 1 1 0%;
}

.min-h-0 {
    min-height: 0px;
}

/* 消息区域滚动优化 */
.messages-scroll {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}
</style>
