<script setup>
import { adoreUser<PERSON><PERSON>, getUserInfo<PERSON>pi, unAdoreUserApi, getCollectedVideosApi } from '@/api/userApi.js'
import { getUserVideosApi } from '@/api/videoApi.js'
import Adores from '@/components/dialog/Adores.vue'
import Info from '@/components/dialog/Info.vue'
import Login from '@/components/dialog/Login.vue'
import UploadVideo from '@/components/dialog/UploadVideo.vue'
import VideoCard from '@/components/VideoCard.vue'
import VideoCardPlus from '@/components/VideoCardPlus.vue'
import { useUserStore } from '@/stores/model/user.js'
import { $dt } from '@primevue/themes'
import { NInfiniteScroll, NTabPane, NTabs } from 'naive-ui'
import { useDialog } from 'primevue/usedialog'
import { onBeforeMount, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const dialog = useDialog()
const router = useRouter()
const userStore = useUserStore()

const videos = ref([])
const collectedVideos = ref([])
const user = reactive({})

const theme = {
    panePaddingLarge: '1.25rem',
    barColor: $dt('sky.500').value,
    tabTextColorHoverBar: $dt('sky.500').value,
    tabTextColorActiveBar: $dt('sky.500').value
}

const adore = () => {
    if (userStore.isLogin) {
        if (user.userIsAdore) unAdoreUserApi(route.params.id)
        else adoreUserApi(route.params.id)
    } else dialog.open(Login)
}

const listAdores = () => dialog.open(Adores)
const uploadVideo = () => dialog.open(UploadVideo)
const editInfo = () => dialog.open(Info, { data: user })

onBeforeMount(() => {
    getUserInfoApi(route.params.id).then((data) => Object.assign(user, data))
    getUserVideosApi(route.params.id).then((data) => videos.value.push(...data))
    getCollectedVideosApi(route.params.id).then((data) => collectedVideos.value.push(...data))
})
</script>

<template>
    <div v-if="user.home !== '隐藏' || user.id === userStore.user.id" class="flex basis-[90%] flex-col overflow-hidden rounded-2xl">
        <div class="flex h-24 items-center justify-between border-b-2 border-b-sky-500 bg-white/50 px-5">
            <div class="flex items-center gap-5">
                <Image
                    preview
                    :src="`http://localhost:8080/api/user/getAvatar/${user.username}`"
                    imageClass="w-16 h-16 rounded-full"
                    pt:previewMask:class="rounded-full" />
                <div class="flex flex-col gap-1">
                    <div class="flex items-center gap-2 text-xl font-bold">
                        {{ user.username }}
                        <i v-if="user.gender !== '不显示'" :class="user.gender === '男' ? 'icon-[prime--mars]' : 'icon-[prime--venus]'" />
                    </div>
                    <p @click="listAdores" class="cursor-pointer text-sm">
                        关注 {{ user.adoresSum }} | 粉丝
                        {{ user.followersSum }}
                    </p>
                    <p class="text-sm">{{ user.description }}</p>
                </div>
            </div>
            <div v-if="user.id === userStore.user.id" class="flex gap-5">
                <Button size="small" label="上传视频" @click="uploadVideo" />
                <Button size="small" label="编辑资料" @click="editInfo" />
            </div>
            <div v-else class="flex gap-5">
                <Button size="small" :label="user.userIsAdore ? '取消关注' : '关注'" @click="adore" />
            </div>
        </div>

        <NTabs size="large" :tabs-padding="10" :theme-overrides="theme" class="bg-white/50">
            <NTabPane name="videos" display-directive="show" class="animate-fadein animate-duration-500">
                <template #tab>
                    <div class="flex items-center gap-1">
                        <i class="icon-[flowbite--clapperboard-play-outline] text-2xl" />
                        <span>我的投稿</span>
                    </div>
                </template>
                <NInfiniteScroll
                    :scrollbar-props="{ contentClass: 'flex flex-wrap gap-5', themeOverrides: { width: '0' } }"
                    class="h-[calc((100vh-3.75rem)*11/12-8.5rem-44px)]">
                    <VideoCardPlus v-for="video in videos" :video="video" class="h-72 w-[calc((100%-5rem)*1/5)]" />
                    <p class="w-full text-center text-gray-500">暂时没有更多视频...</p>
                </NInfiniteScroll>
            </NTabPane>

            <NTabPane name="collections" display-directive="show" class="animate-fadein animate-duration-500">
                <template #tab>
                    <div @click="" class="flex items-center gap-1">
                        <i class="icon-[flowbite--star-outline] text-2xl" />
                        <span>我的收藏</span>
                    </div>
                </template>
                <NInfiniteScroll
                    :scrollbar-props="{ contentClass: 'flex flex-wrap gap-5', themeOverrides: { width: '0' } }"
                    class="h-[calc((100vh-3.75rem)*11/12-8.5rem-44px)]">
                    <VideoCard v-for="video in collectedVideos" :img="true" :video="video" class="h-72 w-[calc((100%-5rem)*1/5)]" />
                    <p v-if="collectedVideos.length === 0" class="w-full text-center text-gray-500">暂时没有收藏的视频...</p>
                </NInfiniteScroll>
            </NTabPane>
        </NTabs>
    </div>
    <div v-else class="flex grow flex-col items-center justify-center gap-5">
        <p class="text-xl font-bold">该用户未公开主页</p>
        <Button label="返回首页" @click="router.push('/')" />
    </div>
</template>

<style scoped></style>
