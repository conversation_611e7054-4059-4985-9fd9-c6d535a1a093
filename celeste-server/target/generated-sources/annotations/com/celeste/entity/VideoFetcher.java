package com.celeste.entity;

import java.lang.Override;
import java.util.function.Consumer;
import org.babyfish.jimmer.internal.GeneratedBy;
import org.babyfish.jimmer.lang.NewChain;
import org.babyfish.jimmer.meta.ImmutableProp;
import org.babyfish.jimmer.sql.ast.table.Table;
import org.babyfish.jimmer.sql.fetcher.Fetcher;
import org.babyfish.jimmer.sql.fetcher.FieldConfig;
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType;
import org.babyfish.jimmer.sql.fetcher.ListFieldConfig;
import org.babyfish.jimmer.sql.fetcher.impl.FetcherImpl;
import org.babyfish.jimmer.sql.fetcher.spi.AbstractTypedFetcher;

@GeneratedBy(
        type = Video.class
)
public class VideoFetcher extends AbstractTypedFetcher<Video, VideoFetcher> {
    public static final VideoFetcher $ = new VideoFetcher(null);

    private VideoFetcher(FetcherImpl<Video> base) {
        super(Video.class, base);
    }

    private VideoFetcher(VideoFetcher prev, ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        super(prev, prop, negative, idOnlyFetchType);
    }

    private VideoFetcher(VideoFetcher prev, ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        super(prev, prop, fieldConfig);
    }

    public static VideoFetcher $from(Fetcher<Video> base) {
        return base instanceof VideoFetcher ? 
        	(VideoFetcher)base : 
        	new VideoFetcher((FetcherImpl<Video>)base);
    }

    @NewChain
    public VideoFetcher createTime() {
        return add("createTime");
    }

    @NewChain
    public VideoFetcher createTime(boolean enabled) {
        return enabled ? add("createTime") : remove("createTime");
    }

    @NewChain
    public VideoFetcher modifyTime() {
        return add("modifyTime");
    }

    @NewChain
    public VideoFetcher modifyTime(boolean enabled) {
        return enabled ? add("modifyTime") : remove("modifyTime");
    }

    @NewChain
    public VideoFetcher betweenTime() {
        return add("betweenTime");
    }

    @NewChain
    public VideoFetcher betweenTime(boolean enabled) {
        return enabled ? add("betweenTime") : remove("betweenTime");
    }

    @NewChain
    public VideoFetcher description() {
        return add("description");
    }

    @NewChain
    public VideoFetcher description(boolean enabled) {
        return enabled ? add("description") : remove("description");
    }

    @NewChain
    public VideoFetcher type() {
        return add("type");
    }

    @NewChain
    public VideoFetcher type(boolean enabled) {
        return enabled ? add("type") : remove("type");
    }

    @NewChain
    public VideoFetcher type(Fetcher<Type> childFetcher) {
        return add("type", childFetcher);
    }

    @NewChain
    public VideoFetcher type(Fetcher<Type> childFetcher,
            Consumer<FieldConfig<Type, TypeTable>> fieldConfig) {
        return add("type", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoFetcher type(IdOnlyFetchType idOnlyFetchType) {
        return add("type", idOnlyFetchType);
    }

    @NewChain
    public VideoFetcher user() {
        return add("user");
    }

    @NewChain
    public VideoFetcher user(boolean enabled) {
        return enabled ? add("user") : remove("user");
    }

    @NewChain
    public VideoFetcher user(Fetcher<User> childFetcher) {
        return add("user", childFetcher);
    }

    @NewChain
    public VideoFetcher user(Fetcher<User> childFetcher,
            Consumer<FieldConfig<User, UserTable>> fieldConfig) {
        return add("user", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoFetcher user(IdOnlyFetchType idOnlyFetchType) {
        return add("user", idOnlyFetchType);
    }

    @NewChain
    public VideoFetcher likes() {
        return add("likes");
    }

    @NewChain
    public VideoFetcher likes(boolean enabled) {
        return enabled ? add("likes") : remove("likes");
    }

    @NewChain
    public VideoFetcher likes(Fetcher<VideoLike> childFetcher) {
        return add("likes", childFetcher);
    }

    @NewChain
    public VideoFetcher likes(Fetcher<VideoLike> childFetcher,
            Consumer<ListFieldConfig<VideoLike, VideoLikeTable>> fieldConfig) {
        return add("likes", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoFetcher comments() {
        return add("comments");
    }

    @NewChain
    public VideoFetcher comments(boolean enabled) {
        return enabled ? add("comments") : remove("comments");
    }

    @NewChain
    public VideoFetcher comments(Fetcher<Comment> childFetcher) {
        return add("comments", childFetcher);
    }

    @NewChain
    public VideoFetcher comments(Fetcher<Comment> childFetcher,
            Consumer<ListFieldConfig<Comment, CommentTable>> fieldConfig) {
        return add("comments", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoFetcher collections() {
        return add("collections");
    }

    @NewChain
    public VideoFetcher collections(boolean enabled) {
        return enabled ? add("collections") : remove("collections");
    }

    @NewChain
    public VideoFetcher collections(Fetcher<Collection> childFetcher) {
        return add("collections", childFetcher);
    }

    @NewChain
    public VideoFetcher collections(Fetcher<Collection> childFetcher,
            Consumer<ListFieldConfig<Collection, CollectionTable>> fieldConfig) {
        return add("collections", childFetcher, fieldConfig);
    }

    @NewChain
    public VideoFetcher collections(IdOnlyFetchType idOnlyFetchType) {
        return add("collections", idOnlyFetchType);
    }

    @NewChain
    public VideoFetcher source() {
        return add("source");
    }

    @NewChain
    public VideoFetcher source(boolean enabled) {
        return enabled ? add("source") : remove("source");
    }

    @NewChain
    public VideoFetcher poster() {
        return add("poster");
    }

    @NewChain
    public VideoFetcher poster(boolean enabled) {
        return enabled ? add("poster") : remove("poster");
    }

    @NewChain
    public VideoFetcher likesSum() {
        return add("likesSum");
    }

    @NewChain
    public VideoFetcher likesSum(boolean enabled) {
        return enabled ? add("likesSum") : remove("likesSum");
    }

    @NewChain
    public VideoFetcher commentsSum() {
        return add("commentsSum");
    }

    @NewChain
    public VideoFetcher commentsSum(boolean enabled) {
        return enabled ? add("commentsSum") : remove("commentsSum");
    }

    @NewChain
    public VideoFetcher collectionsSum() {
        return add("collectionsSum");
    }

    @NewChain
    public VideoFetcher collectionsSum(boolean enabled) {
        return enabled ? add("collectionsSum") : remove("collectionsSum");
    }

    @NewChain
    public VideoFetcher userIsAdore() {
        return add("userIsAdore");
    }

    @NewChain
    public VideoFetcher userIsAdore(boolean enabled) {
        return enabled ? add("userIsAdore") : remove("userIsAdore");
    }

    @NewChain
    public VideoFetcher userIsLike() {
        return add("userIsLike");
    }

    @NewChain
    public VideoFetcher userIsLike(boolean enabled) {
        return enabled ? add("userIsLike") : remove("userIsLike");
    }

    @NewChain
    public VideoFetcher userIsCollect() {
        return add("userIsCollect");
    }

    @NewChain
    public VideoFetcher userIsCollect(boolean enabled) {
        return enabled ? add("userIsCollect") : remove("userIsCollect");
    }

    @Override
    protected VideoFetcher createFetcher(ImmutableProp prop, boolean negative,
            IdOnlyFetchType idOnlyFetchType) {
        return new VideoFetcher(this, prop, negative, idOnlyFetchType);
    }

    @Override
    protected VideoFetcher createFetcher(ImmutableProp prop,
            FieldConfig<?, ? extends Table<?>> fieldConfig) {
        return new VideoFetcher(this, prop, fieldConfig);
    }
}
