package com.celeste.listener;

import com.celeste.entity.Message;
import com.celeste.service.ChatService;
import com.celeste.utils.JwtUtil;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.listener.ConnectListener;
import com.corundumstudio.socketio.listener.DataListener;
import com.corundumstudio.socketio.listener.DisconnectListener;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.StaticLog;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class SocketIOEventListener {

    @Resource
    private SocketIOServer socketIOServer;

    @Resource
    private ChatService chatService;

    // 存储用户ID和SocketIOClient的映射
    private final Map<Long, SocketIOClient> userClients = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 连接事件
        socketIOServer.addConnectListener(new ConnectListener() {
            @Override
            public void onConnect(SocketIOClient client) {
                String token = client.getHandshakeData().getSingleUrlParam("token");
                String userIdParam = client.getHandshakeData().getSingleUrlParam("userId");

                try {
                    Long userId = null;

                    // 优先使用JWT token
                    if (token != null && !token.isEmpty()) {
                        userId = Convert.toLong(JwtUtil.parse(token).getPayload("id"));
                        StaticLog.info("用户 {} 通过JWT认证连接到聊天服务器", userId);
                    }
                    // 如果没有token，尝试使用URL参数中的userId（仅用于测试）
                    else if (userIdParam != null && !userIdParam.isEmpty()) {
                        userId = Convert.toLong(userIdParam);
                        StaticLog.info("用户 {} 通过URL参数连接到聊天服务器（测试模式）", userId);
                    }

                    if (userId != null) {
                        // 如果该用户已经有连接，先断开旧连接
                        SocketIOClient existingClient = userClients.get(userId);
                        if (existingClient != null && existingClient.isChannelOpen()) {
                            StaticLog.info("用户 {} 的旧连接被新连接替换", userId);
                            existingClient.disconnect();
                        }

                        userClients.put(userId, client);
                        client.set("userId", userId);
                        client.joinRoom("user_" + userId);
                        StaticLog.info("用户 {} 成功连接到聊天服务器", userId);
                    } else {
                        StaticLog.error("无法识别用户身份，连接被拒绝");
                        client.disconnect();
                    }
                } catch (Exception e) {
                    StaticLog.error("用户连接失败: {}", e.getMessage());
                    client.disconnect();
                }
            }
        });

        // 断开连接事件
        socketIOServer.addDisconnectListener(new DisconnectListener() {
            @Override
            public void onDisconnect(SocketIOClient client) {
                Long userId = client.get("userId");
                if (userId != null) {
                    userClients.remove(userId);
                    StaticLog.info("用户 {} 断开连接", userId);
                }
            }
        });

        // 发送消息事件
        socketIOServer.addEventListener("sendMessage", String.class, new DataListener<String>() {
            @Override
            public void onData(SocketIOClient client, String data, com.corundumstudio.socketio.AckRequest ackRequest) {
                try {
                    Long senderId = client.get("userId");
                    if (senderId == null) {
                        return;
                    }

                    JSONObject messageData = JSONUtil.parseObj(data);
                    Long recipientId = messageData.getLong("recipientId");
                    String content = messageData.getStr("content");
                    String images = messageData.getStr("images");

                    // 保存消息到数据库
                    Message message = chatService.sendMessage(senderId, recipientId, content, images);

                    // 调试日志
                    StaticLog.info("🔍 SocketIO 调试 - 发送消息:");
                    StaticLog.info("- 客户端 senderId: {}", senderId);
                    StaticLog.info("- 请求 recipientId: {}", recipientId);
                    StaticLog.info("- 数据库消息 senderId: {}", message.senderId());
                    StaticLog.info("- 数据库消息 recipientId: {}", message.recipientId());

                    // 构造消息响应 - 使用数据库中的实际值
                    JSONObject response = JSONUtil.createObj()
                            .set("id", message.id())
                            .set("senderId", message.senderId())
                            .set("recipientId", message.recipientId())
                            .set("content", message.content())
                            .set("images", message.images() != null ? message.images() : "")
                            .set("createTime", message.createTime());

                    // 发送给接收者
                    SocketIOClient recipientClient = userClients.get(recipientId);
                    if (recipientClient != null) {
                        recipientClient.sendEvent("receiveMessage", response.toString());
                    }

                    // 发送确认给发送者
                    client.sendEvent("messageConfirm", response.toString());

                    StaticLog.info("消息发送成功: {} -> {}", senderId, recipientId);
                } catch (Exception e) {
                    StaticLog.error("发送消息失败: {}", e.getMessage());
                    client.sendEvent("messageError", "发送消息失败");
                }
            }
        });

        // 加入聊天房间事件
        socketIOServer.addEventListener("joinChat", String.class, new DataListener<String>() {
            @Override
            public void onData(SocketIOClient client, String data, com.corundumstudio.socketio.AckRequest ackRequest) {
                try {
                    Long userId = client.get("userId");
                    JSONObject chatData = JSONUtil.parseObj(data);
                    Long otherUserId = chatData.getLong("otherUserId");

                    String roomName = "chat_" + Math.min(userId, otherUserId) + "_" + Math.max(userId, otherUserId);
                    client.joinRoom(roomName);

                    StaticLog.info("用户 {} 加入聊天房间: {}", userId, roomName);
                } catch (Exception e) {
                    StaticLog.error("加入聊天房间失败: {}", e.getMessage());
                }
            }
        });

        // 离开聊天房间事件
        socketIOServer.addEventListener("leaveChat", String.class, new DataListener<String>() {
            @Override
            public void onData(SocketIOClient client, String data, com.corundumstudio.socketio.AckRequest ackRequest) {
                try {
                    Long userId = client.get("userId");
                    JSONObject chatData = JSONUtil.parseObj(data);
                    Long otherUserId = chatData.getLong("otherUserId");

                    String roomName = "chat_" + Math.min(userId, otherUserId) + "_" + Math.max(userId, otherUserId);
                    client.leaveRoom(roomName);

                    StaticLog.info("用户 {} 离开聊天房间: {}", userId, roomName);
                } catch (Exception e) {
                    StaticLog.error("离开聊天房间失败: {}", e.getMessage());
                }
            }
        });

        socketIOServer.start();
        StaticLog.info("Socket.IO 服务器启动成功，端口: {}", socketIOServer.getConfiguration().getPort());
    }

    @PreDestroy
    public void destroy() {
        if (socketIOServer != null) {
            socketIOServer.stop();
            StaticLog.info("Socket.IO 服务器已停止");
        }
    }
}
