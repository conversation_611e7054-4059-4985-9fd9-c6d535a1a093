package com.celeste.repository;

import com.celeste.entity.*;
import org.babyfish.jimmer.spring.repository.JRepository;

import java.util.Date;
import java.util.List;

public interface CollectionRepository extends JRepository<Collection, Long> {

    CollectionTable table = CollectionTable.$;

    default Collection insertCollection(long userId, String name) {
        return sql().insert(Objects.createCollection(draft -> draft
                        .setUserId(userId)
                        .setName(name)
                        .setCreateTime(new Date())
                ))
                .getModifiedEntity();
    }

    default List<Collection> listCollectionsByUserId(long userId) {
        return sql().createQuery(table)
                .where(table.userId().eq(userId))
                .orderBy(table.createTime().desc())
                .select(table.fetch(CollectionFetcher.$.name().videosSum()))
                .execute();
    }

    default List<Video> listCollectedVideosByUserId(long userId) {
        VideoTable videoTable = VideoTable.$;
        return sql().createQuery(videoTable)
                .where(videoTable.collections(collection -> collection.userId().eq(userId)))
                .orderBy(videoTable.createTime().desc())
                .select(videoTable.fetch(VideoFetcher.$.description().likesSum().betweenTime().poster().createTime()
                        .user(UserFetcher.$.username())))
                .execute();
    }

}
