package com.celeste.repository;

import com.celeste.entity.*;
import com.celeste.entity.CollectionTable;
import com.celeste.entity.Objects;
import com.celeste.entity.UserFetcher;
import com.celeste.entity.UserProps;
import com.celeste.entity.UserTable;
import com.celeste.entity.dto.RegisterReq;
import com.celeste.entity.dto.ResetPasswordReq;
import com.celeste.entity.dto.UserReq;
import org.babyfish.jimmer.spring.repository.JRepository;
import org.babyfish.jimmer.sql.ast.Predicate;

import java.util.Date;
import java.util.List;

public interface UserRepository extends JRepository<User, Long> {

    UserTable table = UserTable.$;
    CollectionTable collectionTable = CollectionTable.$;

    boolean existsByUsernameOrEmail(String username, String email);

    default User getByUsernameOrEmail(String usernameOrEmail) {
        return sql().createQuery(table)
                .where(Predicate.or(
                        table.username().eq(usernameOrEmail),
                        table.email().eq(usernameOrEmail)))
                .select(table.fetch(UserFetcher.$.username().password().roleList()))
                .fetchOne();
    }

    default int insertUser(RegisterReq req) {
        return sql().insert(Objects.createUser(req.toEntity(), draft ->
                        draft.setCreateTime(new Date())))
                .getTotalAffectedRowCount();
    }

    default int updatePassword(ResetPasswordReq req) {
        return sql().createUpdate(table)
                .set(table.password(), req.getPassword())
                .where(table.email().eq(req.getEmail()))
                .execute();
    }

    default int adoreUser(long adoreId, long followerId) {
        return sql().getAssociations(UserProps.FOLLOWERS).save(adoreId, followerId);
    }

    default int unAdoreUser(long adoreId, long followerId) {
        return sql().getAssociations(UserProps.FOLLOWERS).delete(adoreId, followerId);
    }

    default User listAdoresByUserId(long userId) {
        return sql().createQuery(table)
                .where(table.id().eq(userId))
                .select(table.fetch(UserFetcher.$.adores(UserFetcher.$.username())))
                .fetchOne();
    }

    default User listFollowersByUserId(long userId) {
        return sql().createQuery(table)
                .where(table.id().eq(userId))
                .select(table.fetch(UserFetcher.$.followers(UserFetcher.$.username())))
                .fetchOne();
    }

    default int updateUser(UserReq req, long userId) {
        return sql().update(Objects.createUser(draft -> draft
                        .setId(userId)
                        .setHome(req.getHome())
                        .setGender(req.getGender())
                        .setTypeIds(req.getTypeIds())
                        .setUsername(req.getUsername())
                        .setDescription(req.getDescription())
                ))
                .getTotalAffectedRowCount();
    }

    /**
     * 搜索用户（用于开始新对话）
     */
    default List<User> searchUsers(String keyword, long currentUserId, int page, int size) {
        var query = sql().createQuery(table)
                .where(table.id().ne(currentUserId)); // 排除当前用户

        if (keyword != null && !keyword.trim().isEmpty()) {
            query = query.where(Predicate.or(
                    table.username().ilike("%" + keyword + "%"),
                    table.email().ilike("%" + keyword + "%")
            ));
        }

        return query.select(table.fetch(UserFetcher.$.id().username().email()))
                .limit(size, page * size)
                .execute();
    }

    /**
     * 获取所有用户（除了当前用户）
     */
    default List<User> getAllUsersExceptCurrent(long currentUserId, int page, int size) {
        return sql().createQuery(table)
                .where(table.id().ne(currentUserId))
                .select(table.fetch(UserFetcher.$.id().username().email()))
                .limit(size, page * size)
                .execute();
    }

}
