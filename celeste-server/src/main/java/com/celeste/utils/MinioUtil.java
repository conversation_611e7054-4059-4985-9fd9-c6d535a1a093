package com.celeste.utils;

import cn.hutool.log.StaticLog;
import com.celeste.Application;
import com.feiniaojin.gracefulresponse.GracefulResponseException;
import io.minio.*;
import io.minio.http.Method;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

public class MinioUtil {

    private final static String bucketName = "celeste";
    private static MinioClient client;

    private static MinioClient getClient() {
        if (client == null) {
            try {
                if (Application.applicationContext != null) {
                    client = Application.applicationContext.getBean(MinioClient.class);
                }
            } catch (Exception e) {
                StaticLog.error("MinioClient初始化失败: {}", e.getMessage());
                return null;
            }
        }
        return client;
    }

    public static void getAvatar(String username, HttpServletResponse response) {
        String objectName = "avatar/" + username + ".jpg";
        MinioClient minioClient = getClient();
        if (minioClient == null) {
            StaticLog.error("MinioClient不可用，无法获取头像: {}", objectName);
            return;
        }

        try {
            GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
            IOUtils.copyLarge(object, response.getOutputStream());
        } catch (Exception e) {
            StaticLog.info("头像获取失败:{}", objectName);
        }
    }

    public static void uploadFile(MultipartFile file, String objectName) {
        if (file.isEmpty()) throw new GracefulResponseException("文件大小至少为1字节");
        MinioClient minioClient = getClient();
        if (minioClient == null) {
            StaticLog.error("MinioClient不可用，无法上传文件: {}", objectName);
            return;
        }

        try {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .build());
        } catch (Exception e) {
            StaticLog.info("文件上传失败:{}", objectName);
        }
    }

    public static String getUrl(String objectName) {
        String url = "";
        MinioClient minioClient = getClient();
        if (minioClient == null) {
            StaticLog.error("MinioClient不可用，无法获取URL: {}", objectName);
            return url;
        }

        try {
            url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(1, TimeUnit.DAYS)
                    .build());
        } catch (Exception e) {
            StaticLog.info("Url获取失败:{}", objectName);
        }
        return url;
    }

}
